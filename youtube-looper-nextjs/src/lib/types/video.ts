// Video-related types and interfaces

export interface VideoMetadata {
  id: string
  title: string
  thumbnail: string
  duration: number
  channel?: string
  description?: string
  publishedAt?: string
  viewCount?: number
  url: string
}

// Enhanced video item for draft queue with loop and timeframe support
export interface DraftVideoItem extends VideoMetadata {
  draftId: string // Unique identifier for this draft item (allows same video multiple times)
  loopCount: number // Number of times to loop this video (default: 1)
  startTime?: number // Start time in seconds (optional)
  endTime?: number // End time in seconds (optional)
  addedToDraftAt: number // Timestamp when added to draft
}

export interface VideoSearchResult {
  id: string
  title: string
  description: string
  thumbnail: {
    url: string
    width: number
    height: number
  }
  channel: {
    title: string
    id: string
  }
  duration: string
  publishedAt: string
  viewCount?: number
}

export interface YouTubePlayerState {
  UNSTARTED: -1
  ENDED: 0
  PLAYING: 1
  PAUSED: 2
  BUFFERING: 3
  CUED: 5
}

export interface YouTubePlayer {
  playVideo(): void
  pauseVideo(): void
  stopVideo(): void
  seekTo(seconds: number, allowSeekAhead?: boolean): void
  getCurrentTime(): number
  getDuration(): number
  getPlayerState(): number
  getVideoData(): {
    video_id: string
    title: string
    author: string
  }
  loadVideoById(videoId: string): void
  destroy(): void
}

export interface YouTubePlayerEvent {
  target: YouTubePlayer
  data?: number
}

// YouTube API response types
export interface YouTubeSearchResponse {
  kind: string
  etag: string
  nextPageToken?: string
  prevPageToken?: string
  regionCode: string
  pageInfo: {
    totalResults: number
    resultsPerPage: number
  }
  items: YouTubeVideoItem[]
}

export interface YouTubeVideoItem {
  kind: string
  etag: string
  id: {
    kind: string
    videoId: string
  }
  snippet: {
    publishedAt: string
    channelId: string
    title: string
    description: string
    thumbnails: {
      default: YouTubeThumbnail
      medium: YouTubeThumbnail
      high: YouTubeThumbnail
      standard?: YouTubeThumbnail
      maxres?: YouTubeThumbnail
    }
    channelTitle: string
    liveBroadcastContent: string
    publishTime: string
  }
}

export interface YouTubeThumbnail {
  url: string
  width: number
  height: number
}

export interface YouTubeVideoDetailsResponse {
  kind: string
  etag: string
  items: YouTubeVideoDetails[]
}

export interface YouTubeVideoDetails {
  kind: string
  etag: string
  id: string
  snippet: {
    publishedAt: string
    channelId: string
    title: string
    description: string
    thumbnails: {
      default: YouTubeThumbnail
      medium: YouTubeThumbnail
      high: YouTubeThumbnail
      standard?: YouTubeThumbnail
      maxres?: YouTubeThumbnail
    }
    channelTitle: string
    categoryId: string
    liveBroadcastContent: string
    defaultLanguage?: string
    defaultAudioLanguage?: string
  }
  contentDetails: {
    duration: string
    dimension: string
    definition: string
    caption: string
    licensedContent: boolean
    regionRestriction?: {
      allowed?: string[]
      blocked?: string[]
    }
    contentRating: object
    projection: string
  }
  statistics: {
    viewCount: string
    likeCount?: string
    dislikeCount?: string
    favoriteCount: string
    commentCount?: string
  }
}
