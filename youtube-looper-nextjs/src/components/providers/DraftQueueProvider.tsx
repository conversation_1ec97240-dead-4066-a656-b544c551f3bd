'use client'

import { ReactNode, useState, useEffect } from 'react'
import { DraftQueueContext } from '@/hooks/useDraftQueue'
import { VideoMetadata, DraftVideoItem } from '@/lib/types/video'
import { useAuth } from '@/hooks/useAuth'
import { firebaseService } from '@/lib/services/firebase'
import { getTotalDraftDuration } from '@/lib/utils/time'

interface DraftQueueProviderProps {
  children: ReactNode
}

const DRAFT_QUEUE_STORAGE_KEY = 'draftQueue'

export function DraftQueueProvider({ children }: DraftQueueProviderProps) {
  const { user } = useAuth()
  const [draftItems, setDraftItems] = useState<DraftVideoItem[]>([])
  const [isCreationMode, setIsCreationMode] = useState(false)

  // Load draft queue from localStorage on mount
  useEffect(() => {
    loadDraftFromStorage()
  }, [])

  // Save draft queue to localStorage whenever it changes
  useEffect(() => {
    saveDraftToStorage()
  }, [draftItems])

  const loadDraftFromStorage = () => {
    try {
      const saved = localStorage.getItem(DRAFT_QUEUE_STORAGE_KEY)
      if (saved) {
        const parsed = JSON.parse(saved)
        if (Array.isArray(parsed)) {
          setDraftItems(parsed)
          console.log('📂 Loaded draft queue from storage:', parsed.length, 'videos')
        }
      }
    } catch (error) {
      console.warn('Could not load draft queue from storage:', error)
      setDraftItems([])
    }
  }

  const saveDraftToStorage = () => {
    try {
      localStorage.setItem(DRAFT_QUEUE_STORAGE_KEY, JSON.stringify(draftItems))
    } catch (error) {
      console.warn('Could not save draft queue to storage:', error)
    }
  }

  const clearDraftFromStorage = () => {
    try {
      localStorage.removeItem(DRAFT_QUEUE_STORAGE_KEY)
    } catch (error) {
      console.warn('Could not clear draft queue from storage:', error)
    }
  }

  const addToDraft = (video: VideoMetadata, loopCount: number = 1, startTime?: number, endTime?: number): string => {
    if (!video || !video.id) {
      console.log('Invalid video object')
      return ''
    }

    // Create a unique draft ID for this item (allows same video multiple times)
    const draftId = `${video.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // Create enhanced draft video item
    const draftVideoItem: DraftVideoItem = {
      ...video,
      draftId,
      loopCount: Math.max(1, loopCount), // Ensure minimum loop count of 1
      startTime: startTime && startTime >= 0 ? startTime : undefined,
      endTime: endTime && endTime > 0 && (!startTime || endTime > startTime) ? endTime : undefined,
      addedToDraftAt: Date.now()
    }

    // Add video to draft queue (no duplicate prevention - same video can be added multiple times)
    setDraftItems(prev => [...prev, draftVideoItem])
    console.log(`➕ Added to draft queue: ${video.title} (Loop: ${loopCount}x, ID: ${draftId})`)
    return draftId
  }

  const removeFromDraft = (draftId: string): boolean => {
    const itemIndex = draftItems.findIndex(item => item.draftId === draftId)
    if (itemIndex === -1) {
      console.log('Draft item not found:', draftId)
      return false
    }

    const removedVideo = draftItems[itemIndex]
    setDraftItems(prev => prev.filter(item => item.draftId !== draftId))
    console.log(`➖ Removed from draft queue: ${removedVideo.title} (ID: ${draftId})`)
    return true
  }

  const updateDraftItem = (draftId: string, updates: Partial<Pick<DraftVideoItem, 'loopCount' | 'startTime' | 'endTime'>>): boolean => {
    const itemIndex = draftItems.findIndex(item => item.draftId === draftId)
    if (itemIndex === -1) {
      console.log('Draft item not found for update:', draftId)
      return false
    }

    setDraftItems(prev => prev.map(item => {
      if (item.draftId === draftId) {
        const updatedItem = { ...item, ...updates }
        // Validate loop count
        if (updates.loopCount !== undefined) {
          updatedItem.loopCount = Math.max(1, updates.loopCount)
        }
        // Validate timeframe
        if (updates.startTime !== undefined) {
          updatedItem.startTime = updates.startTime >= 0 ? updates.startTime : undefined
        }
        if (updates.endTime !== undefined) {
          updatedItem.endTime = updates.endTime > 0 && (!updatedItem.startTime || updates.endTime > updatedItem.startTime)
            ? updates.endTime : undefined
        }
        console.log(`🔄 Updated draft item: ${item.title} (ID: ${draftId})`, updates)
        return updatedItem
      }
      return item
    }))
    return true
  }

  const getDraftItem = (draftId: string): DraftVideoItem | undefined => {
    return draftItems.find(item => item.draftId === draftId)
  }

  const clearDraft = () => {
    if (draftItems.length === 0) {
      console.log('Draft queue is already empty')
      return
    }

    const confirmMessage = `Are you sure you want to clear the draft queue?\n\nThis will remove all ${draftItems.length} video(s) from your draft.`
    if (!confirm(confirmMessage)) {
      return
    }

    setDraftItems([])
    console.log('🗑️ Draft queue cleared')
  }

  const isInDraft = (videoId: string): boolean => {
    return draftItems.some(video => video.id === videoId)
  }

  const enterCreationMode = () => {
    setIsCreationMode(true)
    loadDraftFromStorage() // Reload from storage when entering creation mode
    console.log('📝 Entered queue creation mode')
  }

  const exitCreationMode = () => {
    setIsCreationMode(false)
    setDraftItems([])
    clearDraftFromStorage()
    console.log('❌ Exited queue creation mode')
  }

  const saveDraftAsQueue = async (title: string, isPublic = false): Promise<string | null> => {
    if (!user) {
      console.warn('User not authenticated, cannot save queue')
      return null
    }

    if (draftItems.length === 0) {
      console.warn('Cannot save empty draft queue')
      return null
    }

    try {
      // Create a queue state from draft items
      const queueState = {
        items: draftItems.map((draftItem, index) => {
          // Convert DraftVideoItem to QueueItem, filtering out undefined values
          const queueItem: any = {
            id: draftItem.id,
            title: draftItem.title,
            thumbnail: draftItem.thumbnail,
            duration: draftItem.duration,
            channel: draftItem.channel,
            description: draftItem.description,
            publishedAt: draftItem.publishedAt,
            viewCount: draftItem.viewCount,
            url: draftItem.url,
            addedAt: Date.now(),
            queueIndex: index,
            loopCount: draftItem.loopCount
          }

          // Only include startTime and endTime if they are defined
          if (draftItem.startTime !== undefined) {
            queueItem.startTime = draftItem.startTime
          }
          if (draftItem.endTime !== undefined) {
            queueItem.endTime = draftItem.endTime
          }

          return queueItem
        }),
        currentIndex: 0,
        isPlaying: false,
        isLooping: true,
        shuffle: false,
        volume: 1,
        timestamp: Date.now()
      }

      const queueId = await firebaseService.savePersonalQueue(
        user.uid,
        queueState,
        {
          title,
          description: `Queue with ${draftItems.length} videos`,
          isPublic,
        }
      )

      if (queueId) {
        console.log('✅ Draft queue saved to Firebase:', queueId)

        if (isPublic) {
          const publicQueueId = await firebaseService.shareQueue(queueId, user.uid)
          if (publicQueueId) {
            console.log('✅ Draft queue shared publicly:', publicQueueId)
            return publicQueueId
          }
        }

        return queueId
      }

      return null
    } catch (error) {
      console.error('❌ Failed to save draft queue:', error)
      return null
    }
  }

  // Computed properties
  const draftCount = draftItems.length
  const draftDuration = draftItems.reduce((total, draftItem) => {
    return total + getTotalDraftDuration(
      draftItem.duration || 0,
      draftItem.loopCount,
      draftItem.startTime,
      draftItem.endTime
    )
  }, 0)

  const value = {
    draftItems,
    addToDraft,
    removeFromDraft,
    updateDraftItem,
    clearDraft,
    isInDraft,
    getDraftItem,
    draftCount,
    draftDuration,
    isCreationMode,
    enterCreationMode,
    exitCreationMode,
    saveDraftAsQueue,
  }

  return (
    <DraftQueueContext.Provider value={value}>
      {children}
    </DraftQueueContext.Provider>
  )
}
