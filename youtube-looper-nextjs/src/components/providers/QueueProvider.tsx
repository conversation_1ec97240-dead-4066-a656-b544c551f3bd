'use client'

import { ReactNode, useReducer, useMemo, useEffect } from 'react'
import { QueueContext } from '@/hooks/useQueue'
import { QueueState, QueueAction, QueueItem } from '@/lib/types/queue'
import { VideoMetadata } from '@/lib/types/video'
import { firebaseService } from '@/lib/services/firebase'
import { useAuth } from '@/hooks/useAuth'

interface QueueProviderProps {
  children: ReactNode
}

const initialState: QueueState = {
  items: [],
  currentIndex: 0,
  isPlaying: false,
  isLooping: true,
  shuffle: false,
  volume: 1,
  timestamp: Date.now(),
}

function queueReducer(state: QueueState, action: QueueAction): QueueState {
  switch (action.type) {
    case 'ADD_VIDEO':
      const currentItems = Array.isArray(state.items) ? state.items : []
      const now = Date.now()
      const newItem: QueueItem = {
        ...action.payload,
        addedAt: now + Math.random(), // Add small random component to ensure uniqueness
        queueIndex: currentItems.length,
      }
      return {
        ...state,
        items: [...currentItems, newItem],
        timestamp: now,
      }

    case 'REMOVE_VIDEO':
      const itemsToFilter = Array.isArray(state.items) ? state.items : []
      const filteredItems = itemsToFilter.filter((_, index) => index !== action.payload.index)
      return {
        ...state,
        items: filteredItems.map((item, index) => ({ ...item, queueIndex: index })),
        currentIndex: action.payload.index <= state.currentIndex ? Math.max(0, state.currentIndex - 1) : state.currentIndex,
        timestamp: Date.now(),
      }

    case 'MOVE_VIDEO':
      const { fromIndex, toIndex } = action.payload
      const itemsToMove = Array.isArray(state.items) ? [...state.items] : []
      if (fromIndex >= 0 && fromIndex < itemsToMove.length && toIndex >= 0 && toIndex < itemsToMove.length) {
        const [movedItem] = itemsToMove.splice(fromIndex, 1)
        itemsToMove.splice(toIndex, 0, movedItem)
      }
      return {
        ...state,
        items: itemsToMove.map((item, index) => ({ ...item, queueIndex: index })),
        timestamp: Date.now(),
      }

    case 'SET_CURRENT_INDEX':
      const itemsLength = Array.isArray(state.items) ? state.items.length : 0
      return {
        ...state,
        currentIndex: Math.max(0, Math.min(action.payload.index, itemsLength - 1)),
        timestamp: Date.now(),
      }

    case 'SET_PLAYING':
      return {
        ...state,
        isPlaying: action.payload.isPlaying,
        timestamp: Date.now(),
      }

    case 'SET_LOOPING':
      return {
        ...state,
        isLooping: action.payload.isLooping,
        timestamp: Date.now(),
      }

    case 'SET_SHUFFLE':
      return {
        ...state,
        shuffle: action.payload.shuffle,
        timestamp: Date.now(),
      }

    case 'SET_VOLUME':
      return {
        ...state,
        volume: Math.max(0, Math.min(1, action.payload.volume)),
        timestamp: Date.now(),
      }

    case 'CLEAR_QUEUE':
      return {
        ...state,
        items: [],
        currentIndex: 0,
        isPlaying: false,
        timestamp: Date.now(),
      }

    case 'LOAD_QUEUE':
      // Ensure the payload has the correct structure
      const loadedQueue = action.payload

      // Validate that the payload is a valid QueueState
      if (!loadedQueue || typeof loadedQueue !== 'object') {
        console.warn('Invalid queue data provided to LOAD_QUEUE')
        return state
      }

      // Ensure items array exists and is valid
      const items = Array.isArray(loadedQueue.items) ? loadedQueue.items : []

      return {
        ...initialState, // Start with a clean state
        ...loadedQueue,  // Apply the loaded data
        items,           // Ensure items is always an array
        currentIndex: Math.max(0, Math.min(loadedQueue.currentIndex || 0, items.length - 1)),
        timestamp: Date.now(),
      }

    case 'NEXT_VIDEO':
      const nextIndex = state.currentIndex + 1
      const itemsCount = Array.isArray(state.items) ? state.items.length : 0
      return {
        ...state,
        currentIndex: nextIndex >= itemsCount ? (state.isLooping ? 0 : state.currentIndex) : nextIndex,
        timestamp: Date.now(),
      }

    case 'PREVIOUS_VIDEO':
      const prevIndex = state.currentIndex - 1
      const totalItems = Array.isArray(state.items) ? state.items.length : 0
      return {
        ...state,
        currentIndex: prevIndex < 0 ? (state.isLooping ? totalItems - 1 : 0) : prevIndex,
        timestamp: Date.now(),
      }

    default:
      return state
  }
}

export function QueueProvider({ children }: QueueProviderProps) {
  const [state, dispatch] = useReducer(queueReducer, initialState)
  const { user } = useAuth()

  // Auto-save queue to localStorage for persistence
  useEffect(() => {
    const saveToLocalStorage = () => {
      try {
        localStorage.setItem('youtube-looper-queue', JSON.stringify(state))
      } catch (error) {
        console.error('Failed to save queue to localStorage:', error)
      }
    }

    // Debounce saves
    const timeoutId = setTimeout(saveToLocalStorage, 1000)
    return () => clearTimeout(timeoutId)
  }, [state])

  // Load queue from localStorage on mount
  useEffect(() => {
    try {
      const savedQueue = localStorage.getItem('youtube-looper-queue')
      if (savedQueue) {
        const parsedQueue = JSON.parse(savedQueue)
        dispatch({ type: 'LOAD_QUEUE', payload: parsedQueue })
        console.log('✅ Queue loaded from localStorage')
      }
    } catch (error) {
      console.error('Failed to load queue from localStorage:', error)
    }
  }, [])

  // Queue management functions
  const addVideo = (video: VideoMetadata) => {
    dispatch({ type: 'ADD_VIDEO', payload: video })
  }

  const removeVideo = (index: number) => {
    dispatch({ type: 'REMOVE_VIDEO', payload: { index } })
  }

  const moveVideo = (fromIndex: number, toIndex: number) => {
    dispatch({ type: 'MOVE_VIDEO', payload: { fromIndex, toIndex } })
  }

  const clearQueue = () => {
    dispatch({ type: 'CLEAR_QUEUE' })
  }

  const loadQueue = (queue: QueueState) => {
    dispatch({ type: 'LOAD_QUEUE', payload: queue })
  }

  // Playback control functions (placeholders)
  const playVideo = (index?: number) => {
    if (index !== undefined) {
      dispatch({ type: 'SET_CURRENT_INDEX', payload: { index } })
    }
    dispatch({ type: 'SET_PLAYING', payload: { isPlaying: true } })
  }

  const pauseVideo = () => {
    dispatch({ type: 'SET_PLAYING', payload: { isPlaying: false } })
  }

  const nextVideo = () => {
    dispatch({ type: 'NEXT_VIDEO' })
  }

  const previousVideo = () => {
    dispatch({ type: 'PREVIOUS_VIDEO' })
  }

  const seekTo = (seconds: number) => {
    console.log('Seek to:', seconds)
  }

  const setVolume = (volume: number) => {
    dispatch({ type: 'SET_VOLUME', payload: { volume } })
  }

  // State setters
  const setCurrentIndex = (index: number) => {
    dispatch({ type: 'SET_CURRENT_INDEX', payload: { index } })
  }

  const setPlaying = (isPlaying: boolean) => {
    dispatch({ type: 'SET_PLAYING', payload: { isPlaying } })
  }

  const setLooping = (isLooping: boolean) => {
    dispatch({ type: 'SET_LOOPING', payload: { isLooping } })
  }

  const setShuffle = (shuffle: boolean) => {
    dispatch({ type: 'SET_SHUFFLE', payload: { shuffle } })
  }

  // Queue operations with Firebase integration
  const saveQueue = async (title: string, isPublic = false): Promise<string | null> => {
    if (!user) {
      console.warn('User not authenticated, cannot save queue')
      return null
    }

    try {
      const queueId = await firebaseService.savePersonalQueue(
        user.uid,
        state,
        {
          title,
          description: `Queue with ${Array.isArray(state.items) ? state.items.length : 0} videos`,
          isPublic,
        }
      )

      if (queueId) {
        console.log('✅ Queue saved to Firebase:', queueId)

        if (isPublic) {
          const publicQueueId = await firebaseService.shareQueue(queueId, user.uid)
          if (publicQueueId) {
            console.log('✅ Queue shared publicly:', publicQueueId)
            return publicQueueId
          }
        }

        return queueId
      }

      return null
    } catch (error) {
      console.error('❌ Failed to save queue:', error)
      return null
    }
  }

  const shareQueue = async (queueId: string): Promise<string | null> => {
    if (!user) {
      console.warn('User not authenticated, cannot share queue')
      return null
    }

    try {
      const publicQueueId = await firebaseService.shareQueue(queueId, user.uid)
      if (publicQueueId) {
        console.log('✅ Queue shared publicly:', publicQueueId)
        return publicQueueId
      }
      return null
    } catch (error) {
      console.error('❌ Failed to share queue:', error)
      return null
    }
  }

  // Computed properties
  const currentVideo = useMemo(() => {
    // Safety check to ensure items array exists and is valid
    if (!Array.isArray(state.items) || state.items.length === 0) {
      return null
    }

    // Ensure currentIndex is within bounds
    const index = Math.max(0, Math.min(state.currentIndex, state.items.length - 1))
    return state.items[index] || null
  }, [state.items, state.currentIndex])

  const hasNext = useMemo(() => {
    if (!Array.isArray(state.items)) return false
    return state.currentIndex < state.items.length - 1 || state.isLooping
  }, [state.currentIndex, state.items, state.isLooping])

  const hasPrevious = useMemo(() => {
    return state.currentIndex > 0 || state.isLooping
  }, [state.currentIndex, state.isLooping])

  const queueDuration = useMemo(() => {
    if (!Array.isArray(state.items)) return 0
    return state.items.reduce((total, item) => total + (item.duration || 0), 0)
  }, [state.items])

  const value = {
    ...state,
    addVideo,
    removeVideo,
    moveVideo,
    clearQueue,
    loadQueue,
    playVideo,
    pauseVideo,
    nextVideo,
    previousVideo,
    seekTo,
    setVolume,
    setCurrentIndex,
    setPlaying,
    setLooping,
    setShuffle,
    currentVideo,
    hasNext,
    hasPrevious,
    queueDuration,
    saveQueue,
    shareQueue,
  }

  return (
    <QueueContext.Provider value={value}>
      {children}
    </QueueContext.Provider>
  )
}
