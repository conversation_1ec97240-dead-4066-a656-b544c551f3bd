'use client'

import { useDraftQueue } from '@/hooks/useDraftQueue'
import { VideoMetadata } from '@/lib/types/video'
import { formatDuration } from '@/lib/utils/format'

// Sample video data for demonstration
const sampleVideos: VideoMetadata[] = [
  {
    id: 'dQw4w9WgXcQ',
    title: '<PERSON> - Never Gonna Give You Up (Official Video)',
    thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
    duration: 213,
    channel: '<PERSON>',
    url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
  },
  {
    id: 'kJQP7kiw5Fk',
    title: '<PERSON> ft. Daddy <PERSON>',
    thumbnail: 'https://i.ytimg.com/vi/kJQP7kiw5Fk/mqdefault.jpg',
    duration: 282,
    channel: '<PERSON>',
    url: 'https://www.youtube.com/watch?v=kJQP7kiw5Fk'
  },
  {
    id: '9bZkp7q19f0',
    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',
    thumbnail: 'https://i.ytimg.com/vi/9bZkp7q19f0/mqdefault.jpg',
    duration: 253,
    channel: 'officialpsy',
    url: 'https://www.youtube.com/watch?v=9bZkp7q19f0'
  }
]

export function DraftQueueDemo() {
  const { 
    addToDraft, 
    draftItems, 
    draftCount, 
    draftDuration, 
    isCreationMode,
    enterCreationMode,
    exitCreationMode,
    clearDraft
  } = useDraftQueue()

  const handleAddVideo = (video: VideoMetadata, loopCount: number = 1, startTime?: number, endTime?: number) => {
    const draftId = addToDraft(video, loopCount, startTime, endTime)
    console.log('Added video to draft:', { video: video.title, draftId, loopCount, startTime, endTime })
  }

  const handleAddWithCustomSettings = (video: VideoMetadata) => {
    // Add with 3 loops and custom timeframe (30s to 60s)
    handleAddVideo(video, 3, 30, 60)
  }

  return (
    <div className="p-6 space-y-6">
      <div className="glassmorphism rounded-2xl p-6">
        <h2 className="text-2xl font-bold text-white mb-4">Draft Queue Demo</h2>
        <p className="text-dark-300 mb-4">
          This demo showcases the new enhanced create queue features:
        </p>
        <ul className="text-dark-300 space-y-2 mb-6">
          <li>• Same video can be added multiple times</li>
          <li>• Each video can have a custom loop count (default: 1)</li>
          <li>• Each video can have custom start/end times for partial playback</li>
          <li>• Enhanced UI for editing loop count and timeframes</li>
        </ul>

        <div className="flex gap-4 mb-6">
          {!isCreationMode ? (
            <button
              onClick={enterCreationMode}
              className="btn-primary"
            >
              Enter Creation Mode
            </button>
          ) : (
            <button
              onClick={exitCreationMode}
              className="btn-secondary"
            >
              Exit Creation Mode
            </button>
          )}
          
          {isCreationMode && draftCount > 0 && (
            <button
              onClick={clearDraft}
              className="btn-secondary"
            >
              Clear Draft ({draftCount} items)
            </button>
          )}
        </div>

        {isCreationMode && (
          <div className="bg-dark-800/30 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-white mb-2">Draft Queue Stats</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-dark-300">Items:</span>
                <span className="text-white ml-2">{draftCount}</span>
              </div>
              <div>
                <span className="text-dark-300">Total Duration:</span>
                <span className="text-white ml-2">{formatDuration(draftDuration)}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {isCreationMode && (
        <div className="glassmorphism rounded-2xl p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Sample Videos</h3>
          <div className="space-y-3">
            {sampleVideos.map((video) => (
              <div
                key={video.id}
                className="flex items-center gap-4 p-4 bg-dark-800/30 border border-dark-600/30 rounded-lg"
              >
                <img
                  src={video.thumbnail}
                  alt={video.title}
                  className="w-20 h-15 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h4 className="text-white font-medium mb-1">{video.title}</h4>
                  <p className="text-dark-300 text-sm">
                    {formatDuration(video.duration)} • {video.channel}
                  </p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleAddVideo(video)}
                    className="btn-primary text-sm px-3 py-2"
                    title="Add with default settings (1 loop, full duration)"
                  >
                    Add Normal
                  </button>
                  <button
                    onClick={() => handleAddVideo(video, 2)}
                    className="btn-secondary text-sm px-3 py-2"
                    title="Add with 2 loops"
                  >
                    Add 2x Loop
                  </button>
                  <button
                    onClick={() => handleAddWithCustomSettings(video)}
                    className="btn-accent text-sm px-3 py-2"
                    title="Add with 3 loops and custom timeframe (30s-60s)"
                  >
                    Add Custom
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
