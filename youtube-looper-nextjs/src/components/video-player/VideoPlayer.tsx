'use client'

import { useEffect, useRef, useState } from 'react'
import { useQueue } from '@/hooks/useQueue'
import { YouTubePlayer } from '@/lib/types/video'
import { formatTime } from '@/lib/utils/time'

declare global {
  interface Window {
    YT: any
    onYouTubeIframeAPIReady: () => void
    youtubePlayer: YouTubePlayer | null
  }
}

export function VideoPlayer() {
  const {
    currentVideo,
    isPlaying,
    nextVideo,
    setPlaying,
    items,
    currentIndex,
    isLooping
  } = useQueue()
  const playerRef = useRef<HTMLDivElement>(null)
  const [player, setPlayer] = useState<YouTubePlayer | null>(null)
  const [isPlayerReady, setIsPlayerReady] = useState(false)
  const [isPlayerInstanceReady, setIsPlayerInstanceReady] = useState(false)
  const timeMonitorRef = useRef<NodeJS.Timeout | null>(null)
  const videoLoopCountRef = useRef<{ [videoId: string]: number }>({})
  const lastLoadedVideoId = useRef<string | null>(null)

  // Initialize YouTube API
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.YT) {
      console.log('📺 Loading YouTube IFrame API...')

      // Set up the callback for when API is ready
      window.onYouTubeIframeAPIReady = () => {
        console.log('✅ YouTube IFrame API ready')
        setIsPlayerReady(true)
      }

      // Load the API
      const tag = document.createElement('script')
      tag.src = 'https://www.youtube.com/iframe_api'
      const firstScriptTag = document.getElementsByTagName('script')[0]
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
    } else if (window.YT) {
      setIsPlayerReady(true)
    }
  }, [])

  // Create player when API is ready and we have a video
  useEffect(() => {
    if (isPlayerReady && currentVideo && playerRef.current && !player) {
      console.log('🎵 Creating YouTube player for:', currentVideo.title)

      const newPlayer = new window.YT.Player(playerRef.current, {
        height: '100%',
        width: '100%',
        videoId: currentVideo.id,
        playerVars: {
          autoplay: 1,
          controls: 1,
          rel: 0,
          modestbranding: 1,
          fs: 1,
          cc_load_policy: 0,
          iv_load_policy: 3,
          autohide: 0
        },
        events: {
          onReady: (event: any) => {
            console.log('✅ YouTube player ready')
            setIsPlayerInstanceReady(true)

            // Don't auto-play here - let the useEffect handle video loading with proper start time
            // event.target.playVideo()
            // setPlaying(true)
          },
          onStateChange: (event: any) => {
            const state = event.data
            console.log('🎵 Player state changed:', state)

            // YouTube player states:
            // -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)

            if (state === 1) { // Playing
              setPlaying(true)

              // Fallback: If video just started playing and we have a start time,
              // check if we need to seek (especially for new videos)
              if (currentVideo?.startTime && lastLoadedVideoId.current === currentVideo.id) {
                const currentTime = event.target.getCurrentTime()
                const startTime = currentVideo.startTime
                // If we're not close to the start time (tolerance of 2 seconds), seek to it
                if (Math.abs(currentTime - startTime) > 2) {
                  console.log(`⏰ Fallback seeking from ${currentTime}s to ${startTime}s for video ${currentVideo.id}`)
                  event.target.seekTo(startTime, true)
                }
              }
            } else if (state === 2) { // Paused
              setPlaying(false)
            } else if (state === 0) { // Ended
              setPlaying(false)
              handleVideoEnded()
            }
          },
          onError: (event: any) => {
            console.error('❌ YouTube player error:', event.data)
            // Try to skip to next video on error
            handleVideoEnded()
          }
        }
      })

      setPlayer(newPlayer)
      window.youtubePlayer = newPlayer
    }
  }, [isPlayerReady, currentVideo, player])

  // Reset player instance ready state when player changes
  useEffect(() => {
    if (!player) {
      setIsPlayerInstanceReady(false)
    }
  }, [player])

  // Handle video ended - move to next video or handle looping
  const handleVideoEnded = () => {
    console.log('🔄 Video ended')

    // Stop time monitoring
    if (timeMonitorRef.current) {
      clearInterval(timeMonitorRef.current)
      timeMonitorRef.current = null
    }

    if (items.length > 0) {
      const currentItem = items[currentIndex]
      const videoId = currentItem.id

      // Get current loop count for this video (default to 0)
      const currentLoops = videoLoopCountRef.current[videoId] || 0
      const targetLoops = currentItem.loopCount || 1

      console.log(`🔍 Video ${videoId}: completed ${currentLoops + 1}/${targetLoops} loops`)

      if (currentLoops + 1 < targetLoops) {
        // Loop this video again
        videoLoopCountRef.current[videoId] = currentLoops + 1
        console.log(`🔁 Looping video again: ${currentLoops + 1}/${targetLoops}`)

        // Simply seek to start time if it exists, otherwise restart from beginning
        if (player) {
          if (currentItem.startTime) {
            player.seekTo(currentItem.startTime, true)
          } else {
            player.seekTo(0, true)
          }

          // Restart monitoring if needed
          if (currentItem.endTime) {
            setTimeout(() => startTimeMonitoring(), 500)
          }
        }
        return
      }

      // Video has completed all its loops, move to next
      console.log(`✅ Video completed all ${targetLoops} loops, moving to next`)

      const nextIndex = currentIndex + 1
      if (nextIndex < items.length) {
        nextVideo()
      } else if (isLooping) {
        nextVideo() // Queue loop
      } else {
        setPlaying(false)
        console.log('🏁 Queue finished')
      }
    }
  }

  // Start time monitoring for videos with end times
  const startTimeMonitoring = () => {
    if (!currentVideo?.endTime || !player) return

    // Clear any existing monitor
    if (timeMonitorRef.current) {
      clearInterval(timeMonitorRef.current)
    }

    // Monitor playback time every 500ms
    timeMonitorRef.current = setInterval(() => {
      try {
        if (player && typeof player.getCurrentTime === 'function') {
          const currentTime = player.getCurrentTime()
          const endTime = currentVideo.endTime!

          // Check if we've reached the end time (with 0.5s tolerance)
          if (currentTime >= endTime - 0.5) {
            console.log(`⏰ Reached end time ${endTime}s at ${currentTime}s`)

            // Stop monitoring
            if (timeMonitorRef.current) {
              clearInterval(timeMonitorRef.current)
              timeMonitorRef.current = null
            }

            // Handle as if video ended
            handleVideoEnded()
          }
        }
      } catch (error) {
        console.error('❌ Error monitoring video time:', error)
      }
    }, 500)
  }

  // Reset loop counters when queue changes (new session)
  useEffect(() => {
    console.log('🔄 Queue changed, resetting all loop counters')
    videoLoopCountRef.current = {}
  }, [items])

  // Update player when current video changes
  useEffect(() => {
    if (player && isPlayerInstanceReady) {
      if (currentVideo && currentVideo.id) {
        console.log('🔄 Loading new video:', currentVideo.title)

        // Initialize loop count for this video if not exists
        if (!(currentVideo.id in videoLoopCountRef.current)) {
          videoLoopCountRef.current[currentVideo.id] = 0
          console.log(`🆕 Initialized loop counter for ${currentVideo.id}: 0`)
        }

        try {
          console.log(`⏰ Loading video ${currentVideo.id} with startTime: ${currentVideo.startTime || 0}s`)

          // Track which video we're loading
          lastLoadedVideoId.current = currentVideo.id

          // Load video and start playing
          player.loadVideoById(currentVideo.id)

          // Start playing after a short delay
          setTimeout(() => {
            if (player && lastLoadedVideoId.current === currentVideo.id) {
              player.playVideo()
              setPlaying(true)

              // If video has start time, seek to it
              if (currentVideo.startTime) {
                setTimeout(() => {
                  if (player && typeof player.seekTo === 'function' && lastLoadedVideoId.current === currentVideo.id) {
                    console.log(`🎯 Seeking to start time: ${currentVideo.startTime}s for video ${currentVideo.id}`)
                    player.seekTo(currentVideo.startTime, true)
                  }
                }, 500)
              }

              // Start time monitoring if video has end time
              if (currentVideo.endTime) {
                setTimeout(() => startTimeMonitoring(), 1000)
              }
            }
          }, 500)
        } catch (error) {
          console.error('❌ Error loading video:', error)
        }
      } else {
        // No current video - destroy player
        console.log('🛑 No current video, destroying player')
        try {
          player.destroy()
          setPlayer(null)
          setIsPlayerInstanceReady(false)
          window.youtubePlayer = null
          if (timeMonitorRef.current) {
            clearInterval(timeMonitorRef.current)
            timeMonitorRef.current = null
          }
        } catch (error) {
          console.error('❌ Error destroying player:', error)
        }
      }
    }
  }, [player, isPlayerInstanceReady, currentVideo])

  // Control player based on isPlaying state
  useEffect(() => {
    // Don't try to control player if there's no current video (queue cleared)
    if (!currentVideo || !player || !isPlayerInstanceReady) {
      return
    }

    // Additional safety check to ensure player methods exist
    if (typeof player.getPlayerState !== 'function' ||
        typeof player.playVideo !== 'function' ||
        typeof player.pauseVideo !== 'function') {
      return
    }

    try {
      const playerState = player.getPlayerState()

      if (isPlaying && playerState !== 1) { // Not playing
        player.playVideo()
      } else if (!isPlaying && playerState === 1) { // Currently playing
        player.pauseVideo()
      }
    } catch (error) {
      console.error('❌ Error controlling player:', error)
    }
  }, [player, isPlayerInstanceReady, isPlaying, currentVideo])

  // Cleanup time monitoring on unmount
  useEffect(() => {
    return () => {
      if (timeMonitorRef.current) {
        clearInterval(timeMonitorRef.current)
        timeMonitorRef.current = null
      }
    }
  }, [])

  return (
    <div className="glassmorphism rounded-2xl overflow-hidden">
      <div className="aspect-video bg-black relative">
        {currentVideo ? (
          <div
            ref={playerRef}
            className="w-full h-full"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-dark-400">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
              <p className="text-lg font-medium">No video selected</p>
              <p className="text-sm">Add videos to your queue to start playing</p>
            </div>
          </div>
        )}
      </div>

      {currentVideo && (
        <div className="p-4 border-t border-white/10">
          <h3 className="font-medium text-white truncate mb-1">
            {currentVideo.title}
          </h3>
          <p className="text-sm text-dark-300 truncate mb-2">
            {currentVideo.channel || 'Unknown Channel'}
          </p>

          {/* Loop and timeframe information */}
          {(currentVideo.startTime || currentVideo.endTime || (currentVideo.loopCount && currentVideo.loopCount > 1)) && (
            <div className="flex flex-wrap gap-2 text-xs">
              {/* Timeframe info */}
              {(currentVideo.startTime || currentVideo.endTime) && (
                <div className="bg-primary-600/20 text-primary-300 px-2 py-1 rounded-md flex items-center gap-1">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  <span>
                    {currentVideo.startTime ? formatTime(currentVideo.startTime) : '0:00'}
                    {' - '}
                    {currentVideo.endTime ? formatTime(currentVideo.endTime) : 'end'}
                  </span>
                </div>
              )}

              {/* Loop count info */}
              {currentVideo.loopCount && currentVideo.loopCount > 1 && (
                <div className="bg-accent-600/20 text-accent-300 px-2 py-1 rounded-md flex items-center gap-1">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                  </svg>
                  <span>{(videoLoopCountRef.current[currentVideo.id] || 0) + 1}/{currentVideo.loopCount}</span>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
